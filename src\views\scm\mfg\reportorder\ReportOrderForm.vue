<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="60%">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
      :inline="true"
    >
      <el-form-item label="生产编号" prop="workNo">
        <el-input v-model="formData.workNo" placeholder="请输入生产编号" class="!w-240px" disabled/>
      </el-form-item>
      <el-form-item label="任务类型" prop="type">
        <el-select v-model="formData.type" placeholder="请选择任务类型"  class="!w-240px">
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.MFG_WORK_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="报工编号" prop="reportCode">
        <el-input v-model="formData.reportCode" placeholder="保存自动生成" clearable class="!w-240px" disabled/>
      </el-form-item>
      <el-form-item label="开始时间" prop="startTime">
        <el-date-picker
          v-model="formData.startTime"
          type="datetime"
          value-format="x"
          placeholder="选择开始时间"
          clearable class="!w-240px"
          :disabledDate="disabledStartDate"
          @change="calculateCostTime"
        />
      </el-form-item>
      <el-form-item label="结束时间" prop="endTime">
        <el-date-picker
          v-model="formData.endTime"
          type="datetime"
          value-format="x"
          placeholder="选择结束时间" class="!w-240px"
          :disabledDate="disabledEndDate"
          @change="calculateCostTime"
        />
      </el-form-item>
      <el-form-item label="用时" prop="costTime">
        <div class="duration-input-group">
          <el-input
            v-model="durationHoursDisplay"
            placeholder="0"
            class="duration-input"
            @input="handleHoursInput"
            @blur="updateCostTime"
          />
          <span class="duration-label">小时</span>
          <el-input
            v-model="durationMinutesDisplay"
            placeholder="0"
            class="duration-input"
            @input="handleMinutesInput"
            @blur="updateCostTime"
          />
          <span class="duration-label">分钟</span>
        </div>
      </el-form-item>
      <el-form-item label="人数" prop="costHeadcount">
        <el-input v-model="formData.costHeadcount" placeholder="请输入人数" class="!w-240px"/>
      </el-form-item>
      <el-form-item label="生产线" prop="line">
        <el-select v-model="formData.line" placeholder="请选择生产线" clearable class="!w-240px" @change="handleLineChange">
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.MANUFACTURE_LINE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="数量" prop="quantity">
        <el-input v-model="formData.quantity" placeholder="请输入数量" class="!w-240px">
          <template #suffix>
            <span class="text-gray-500">{{ productUnitName }}</span>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="件数" prop="piece">
        <el-input v-model="formData.piece" placeholder="请输入件数" class="!w-240px"/>
      </el-form-item>
      <el-form-item label="批号" prop="batchNo">
        <el-input v-model="formData.batchNo" placeholder="请输入批号" class="!w-240px"/>
      </el-form-item>
      <el-form-item label="温度" prop="temperature">
        <el-input v-model="formData.temperature" placeholder="请输入温度" class="!w-240px"/>
      </el-form-item>
      <el-form-item label="湿度" prop="humidity">
        <el-input v-model="formData.humidity" placeholder="请输入湿度" class="!w-240px"/>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入备注" class="!w-240px"/>
      </el-form-item>
    </el-form>

    <!-- 子表的表单 -->
    <el-tabs v-model="subTabsName" class="mt-20px">
      <el-tab-pane label="报工损耗" name="reportLoss">
        <ReportLossDetailForm
          ref="reportLossDetailFormRef"
          :report-id="formData.id"
          :work-id="formData.workId"
          :disabled="formType === 'view'"
        />
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { ReportOrderApi, ReportOrderVO } from '@/api/scm/mfg/reportorder'
import { WorkOrderApi, WorkOrderVO } from '@/api/scm/mfg/workorder'
import { TempHumidityApi } from '@/api/scm/iot/device/temphumidity'
import { UnitApi } from '@/api/scm/base/unit'
import { formatTime } from '@/utils'

import ReportLossDetailForm from './components/ReportLossDetailForm.vue'


// 在文件顶部定义类型接口，补充formData类型定义
interface FormDataType {
  id?: number;
  workId?: number;
  workNo?: string;
  type?: string;
  reportCode?: string;
  startTime?: number | undefined;
  endTime?: number | undefined;
  costTime?: number;
  costHeadcount?: number;
  line?: string;
  quantity?: number;
  piece?: number;
  batchNo?: string;
  temperature?: number;
  humidity?: number;
  remark?: string;
}

/** 报工 表单 */
defineOptions({ name: 'ReportOrderForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const workOrderData = ref<WorkOrderVO | null>(null) // 保存工单数据用于比较
const productUnitName = ref('') // 产品单位名称

/** 子表的表单 */
const subTabsName = ref('reportLoss')
const reportLossDetailFormRef = ref()
const formData = ref<FormDataType>({
  id: undefined,
  workId: undefined,
  workNo: undefined,
  type: undefined,
  reportCode: undefined,
  startTime: undefined,
  endTime: undefined,
  costTime: undefined,
  costHeadcount: undefined,
  line: undefined,
  quantity: undefined,
  piece: undefined,
  batchNo: undefined,
  temperature: undefined,
  humidity: undefined,
  remark: undefined,
})

// 用时输入相关变量
const durationHoursDisplay = ref<string>('0')
const durationMinutesDisplay = ref<string>('0')

const formRules = reactive({
  type: [{ required: true, message: '任务类型不能为空', trigger: 'change' }],
  startTime: [{ required: true, message: '开始时间不能为空', trigger: 'blur' }],
  endTime: [
    { required: true, message: '结束时间不能为空', trigger: 'blur' },
    {
      validator: (_rule: any, value: any, callback: any) => {
        if (value && formData.value.startTime && value < formData.value.startTime) {
          callback(new Error('结束时间不能早于开始时间'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  quantity: [{ required: true, message: '数量不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref

// 获取单位名称
const getUnitName = async (unitId: number) => {
  if (!unitId) return ''
  try {
    const unitInfo = await UnitApi.getUnit(unitId)
    return unitInfo?.name || ''
  } catch (error) {
    console.error('获取单位信息失败:', error)
    return ''
  }
}

// 禁用开始时间的日期选择（只允许选择当前时间之前的时间）
const disabledStartDate = (time: Date) => {
  return time.getTime() > Date.now()
}

// 禁用结束时间的日期选择（不允许选择开始时间之前的时间）
const disabledEndDate = (time: Date) => {
  // 如果没有选择开始时间，则不限制结束时间
  if (!formData.value.startTime) {
    return false
  }
  // 结束时间不能早于开始时间
  return time.getTime() < formData.value.startTime
}

// 处理小时输入
const handleHoursInput = (value: string) => {
  // 只允许数字输入
  const numValue = value.replace(/[^\d]/g, '')
  durationHoursDisplay.value = numValue
}

// 处理分钟输入
const handleMinutesInput = (value: string) => {
  // 只允许数字输入，并限制最大值为59
  let numValue = value.replace(/[^\d]/g, '')
  if (parseInt(numValue) > 59) {
    numValue = '59'
  }
  durationMinutesDisplay.value = numValue
}

// 手动更新用时（从小时分钟输入框）
const updateCostTime = () => {
  const hours = parseInt(durationHoursDisplay.value) || 0
  const minutes = parseInt(durationMinutesDisplay.value) || 0
  const totalMinutes = hours * 60 + minutes
  formData.value.costTime = totalMinutes
}

// 从用时分钟数更新小时分钟输入框
const updateDurationInputs = (totalMinutes: number) => {
  const hours = Math.floor(totalMinutes / 60)
  const minutes = totalMinutes % 60
  durationHoursDisplay.value = hours.toString()
  durationMinutesDisplay.value = minutes.toString()
}

// 计算用时（从开始结束时间自动计算）
const calculateCostTime = () => {
  if (formData.value.startTime && formData.value.endTime) {
    const startTime = new Date(formData.value.startTime)
    const endTime = new Date(formData.value.endTime)

    // 计算时间差（毫秒）
    const timeDiff = endTime.getTime() - startTime.getTime()

    if (timeDiff > 0) {
      // 转换为分钟，精确到分钟
      const minutes = Math.round(timeDiff / (1000 * 60))

      // 更新用时字段（存储分钟数）
      formData.value.costTime = minutes

      // 更新小时分钟输入框
      updateDurationInputs(minutes)
    } else {
      formData.value.costTime = 0
      durationHoursDisplay.value = '0'
      durationMinutesDisplay.value = '0'
    }

    // 计算用时后，如果生产线已选择，则自动获取温湿度数据
    if (formData.value.line) {
      fetchTempHumidityData()
    }
  } else {
    formData.value.costTime = 0
    durationHoursDisplay.value = '0'
    durationMinutesDisplay.value = '0'
  }

  // 重新验证结束时间字段，确保时间验证规则生效
  nextTick(() => {
    if (formRef.value) {
      formRef.value.validateField('endTime')
    }
  })
}

// 获取温湿度数据
const fetchTempHumidityData = async () => {
  try {
    // 检查必要参数是否存在
    if (!formData.value.line || !formData.value.startTime || !formData.value.endTime) {
      return
    }

    const params = {
      line: formData.value.line,
      startTime: formatTime(formData.value.startTime, 'yyyy-MM-dd HH:mm:ss'),
      endTime: formatTime(formData.value.endTime, 'yyyy-MM-dd HH:mm:ss')
    }

    const res = await TempHumidityApi.getTempHumiditySimpleInfo(params)
    if (res) {
      // 填充温湿度数据到表单
      formData.value.temperature = res.temperature
      formData.value.humidity = res.humidity
    }
  } catch (error) {
    // 获取失败不阻止用户操作，用户可以手动输入
    message.warning('获取温湿度数据失败，请手动输入')
  }
}

// 处理生产线变化事件
const handleLineChange = async (value: string) => {
  if (value) {
    // 当生产线选择后，如果开始时间和结束时间都已填写，则自动查询温湿度
    if (formData.value.startTime && formData.value.endTime) {
      await fetchTempHumidityData()
    }
  } else {
    // 清空生产线时，清空温湿度数据
    formData.value.temperature = undefined
    formData.value.humidity = undefined
  }
}

/** 打开弹窗 */
const open = async (type: string, id?: number, workOrderId?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()

  // 如果传入了工单ID，自动填充工单相关信息
  if (workOrderId && type === 'create') {
    formLoading.value = true
    try {
      const data = await WorkOrderApi.getWorkOrder(workOrderId)
      workOrderData.value = data // 保存工单数据用于后续比较
      // 自动填充工单相关字段
      formData.value.workId = data.id
      formData.value.workNo = data.workNo
      formData.value.line = data.scheduleLine || data.actualLine
      formData.value.quantity = data.scheduleQuantity
      formData.value.piece = data.schedulePiece
      formData.value.costHeadcount = data.scheduleHeadcount
      formData.value.batchNo = formatTime(new Date(), 'yyyyMMdd') // batchNo的值为当前的年月日
      formData.value.type = '2' // 生产
      // 填充开始时间
      if (data.scheduleStartTime) {
        formData.value.startTime = new Date(data.scheduleStartTime).getTime()
      } else if (data.actualStartTime) {
        formData.value.startTime = new Date(data.actualStartTime).getTime()
      }

      // 填充结束时间
      if (data.scheduleEndTime) {
        formData.value.endTime = new Date(data.scheduleEndTime).getTime()
      } else if (data.actualEndTime) {
        formData.value.endTime = new Date(data.actualEndTime).getTime()
      }

      // 计算用时
      calculateCostTime()

      // 设置产品单位名称
      if (data.productUnit) {
        productUnitName.value = await getUnitName(data.productUnit)
      }
    } catch (error) {
      console.error('获取工单信息失败:', error)
    } finally {
      formLoading.value = false
    }
  }

  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      const reportData = await ReportOrderApi.getReportOrder(id)
      formData.value = reportData

      // 处理用时数据：将字符串转换为分钟数，并更新小时分钟输入框
      if (reportData.costTime) {
        const minutes = parseInt(reportData.costTime, 10) || 0
        formData.value.costTime = minutes
        updateDurationInputs(minutes)
      }

      // 如果是修改操作，也需要获取对应的工单数据用于比较
      if (formData.value.workId) {
        const data = await WorkOrderApi.getWorkOrder(formData.value.workId)
        workOrderData.value = data

        // 设置产品单位名称
        if (data.productUnit) {
          productUnitName.value = await getUnitName(data.productUnit)
        }
      }
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()

  // 校验子表
  if (reportLossDetailFormRef.value) {
    await reportLossDetailFormRef.value.validate()
  }
  
  // 提交请求
  formLoading.value = true
  try {
    // 获取损耗数据
    let lossDetails: any[] = []
    if (reportLossDetailFormRef.value) {
      const reportLossData = reportLossDetailFormRef.value.getData()
      if (reportLossData && reportLossData.length > 0) {
        // 转换损耗数据格式，只保留必要字段
        lossDetails = reportLossData.map((item: any) => ({
          id: item.id,
          materialId: item.materialId,
          materialName: item.materialName,
          materialCode: item.materialCode,
          spec: item.spec,
          lossQuantity: item.lossQuantity,
          lossUnit: item.lossUnit,
          remark: item.remark || ''
        }))
      }
    }

    // 准备数据
    const submitData: ReportOrderVO = {
      id: formData.value.id ?? 0,
      workId: formData.value.workId ?? 0,
      workNo: formData.value.workNo ?? '',
      type: formData.value.type ?? '',
      reportCode: formData.value.reportCode ?? '',
      startTime: formData.value.startTime ?? Date.now(),
      endTime: formData.value.endTime ?? Date.now(),
      costTime: (formData.value.costTime ?? 0).toString(),
      costHeadcount: Number(formData.value.costHeadcount ?? 0),
      line: formData.value.line ?? '',
      quantity: Number(formData.value.quantity ?? 0),
      piece: Number(formData.value.piece ?? 0),
      batchNo: formData.value.batchNo ?? '',
      temperature: Number(formData.value.temperature ?? 0),
      humidity: Number(formData.value.humidity ?? 0),
      remark: formData.value.remark ?? '',
      // 添加slotNo字段以满足API要求，但在表单中不显示
      slotNo: 0,
      // 包含损耗明细数据
      lossDetails: lossDetails
    };

    if (formType.value === 'create') {
      await ReportOrderApi.createReportOrder(submitData)
      message.success(t('common.createSuccess'))
    } else {
      await ReportOrderApi.updateReportOrder(submitData)
      message.success(t('common.updateSuccess'))
    }

    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    workId: undefined,
    workNo: undefined,
    type: undefined,
    reportCode: undefined,
    startTime: undefined,
    endTime: undefined,
    costTime: undefined,
    costHeadcount: undefined,
    line: undefined,
    quantity: undefined,
    piece: undefined,
    batchNo: undefined,
    temperature: undefined,
    humidity: undefined,
    remark: undefined,
  }
  workOrderData.value = null
  productUnitName.value = ''
  formRef.value?.resetFields()
}
</script>

<style scoped>
.duration-input-group {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.duration-input {
  width: 80px !important;
}

.duration-label {
  font-size: 14px;
  color: #606266;
  margin-right: 8px;
}



@media (max-width: 768px) {
  .duration-input-group {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .duration-input {
    width: 100px !important;
  }

  .duration-label {
    margin-right: 0;
    margin-left: 4px;
  }
}
</style>
