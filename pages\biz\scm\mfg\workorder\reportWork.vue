<template>
	<view class="report-work-page">
		<!-- 工单信息展示 -->
		<view class="work-order-info" v-if="workOrderData">
			<view class="info-header">
				<text class="header-title">工单信息</text>
			</view>
			<view class="info-content">
				<view class="info-item">
					<text class="label">生产单号：</text>
					<text class="value">{{ workOrderData.workNo || '暂无单号' }}</text>
				</view>
				<view class="info-item">
					<text class="label">产品名称：</text>
					<text class="value">{{ workOrderData.productName || '未知产品' }}</text>
				</view>
				<view class="info-item" v-if="workOrderData.productCode">
					<text class="label">产品编码：</text>
					<text class="value">{{ workOrderData.productCode }}</text>
				</view>
				<view class="info-item">
					<text class="label">计划数量：</text>
					<text class="value">{{ formatQuantity(workOrderData.scheduleQuantity) }}</text>
				</view>
				<view class="info-item" v-if="workOrderData.deliverDate">
					<text class="label">交期：</text>
					<text class="value">{{ formatDate(workOrderData.deliverDate) }}</text>
				</view>
			</view>
		</view>

		<!-- 报工表单 -->
		<view class="report-form">
			<view class="form-header">
				<text class="header-title">报工信息</text>
			</view>
			<view class="form-content">
				<!-- 这里可以添加报工表单字段 -->
				<view class="form-item">
					<text class="form-label">完成数量：</text>
					<input class="form-input" type="number" placeholder="请输入完成数量" />
				</view>
				<view class="form-item">
					<text class="form-label">报工说明：</text>
					<textarea class="form-textarea" placeholder="请输入报工说明"></textarea>
				</view>
			</view>
		</view>

		<!-- 操作按钮 -->
		<view class="action-buttons">
			<button class="btn btn-primary" @click="submitReport">提交报工</button>
			<button class="btn btn-secondary" @click="goBack">返回</button>
		</view>
	</view>
</template>

<script>
export default {
	name: 'ReportWork',
	data() {
		return {
			// 从上一页面接收的工单数据
			workOrderData: null,
			// 操作类型
			operationType: '',
			// 接收数据的时间戳
			timestamp: null
		}
	},

	onLoad() {
		// 获取页面间通信的 eventChannel
		const eventChannel = this.getOpenerEventChannel();

		// 监听来自上一页面的数据
		eventChannel.on('acceptDataFormOpener', (data) => {
			console.log('报工页面接收到数据:', data);

			// 保存接收到的数据
			this.workOrderData = data.workOrderData;
			this.operationType = data.operationType;
			this.timestamp = data.timestamp;

			// 可以在这里进行数据验证
			this.validateReceivedData();
		});
	},

	methods: {
		// 验证接收到的数据
		validateReceivedData() {
			if (!this.workOrderData) {
				uni.showToast({
					title: '未获取到工单数据',
					icon: 'none'
				});
				return;
			}

			console.log('工单数据验证通过:', this.workOrderData);
		},

		// 格式化数量
		formatQuantity(value) {
			if (value === null || value === undefined || value === '') return '0';
			if (isNaN(value)) return '0';
			return Number(value).toLocaleString();
		},

		// 格式化日期
		formatDate(timestamp) {
			if (!timestamp) return '-';
			const date = new Date(timestamp);
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			return `${year}-${month}-${day}`;
		},

		// 提交报工
		submitReport() {
			// 这里添加提交报工的逻辑
			uni.showToast({
				title: '报工提交成功',
				icon: 'success'
			});
		},

		// 返回上一页
		goBack() {
			uni.navigateBack();
		}
	}
}
</script>

<style lang="scss" scoped>
.report-work-page {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

.work-order-info, .report-form {
	background-color: white;
	border-radius: 12rpx;
	margin-bottom: 20rpx;
	overflow: hidden;
}

.info-header, .form-header {
	padding: 20rpx;
	background-color: #f8f9fa;
	border-bottom: 1px solid #e5e5e5;

	.header-title {
		font-size: 28rpx;
		font-weight: 600;
		color: #333;
	}
}

.info-content, .form-content {
	padding: 20rpx;
}

.info-item {
	display: flex;
	align-items: center;
	margin-bottom: 16rpx;

	&:last-child {
		margin-bottom: 0;
	}

	.label {
		font-size: 26rpx;
		color: #666;
		width: 160rpx;
		flex-shrink: 0;
	}

	.value {
		font-size: 26rpx;
		color: #333;
		flex: 1;
	}
}

.form-item {
	margin-bottom: 24rpx;

	&:last-child {
		margin-bottom: 0;
	}

	.form-label {
		display: block;
		font-size: 26rpx;
		color: #333;
		margin-bottom: 12rpx;
	}

	.form-input, .form-textarea {
		width: 100%;
		padding: 16rpx;
		border: 1px solid #ddd;
		border-radius: 8rpx;
		font-size: 26rpx;
		box-sizing: border-box;

		&:focus {
			border-color: #007bff;
		}
	}

	.form-textarea {
		height: 120rpx;
		resize: none;
	}
}

.action-buttons {
	padding: 20rpx 0;

	.btn {
		width: 100%;
		height: 88rpx;
		border-radius: 8rpx;
		font-size: 28rpx;
		margin-bottom: 16rpx;
		border: none;

		&:last-child {
			margin-bottom: 0;
		}

		&.btn-primary {
			background-color: #007bff;
			color: white;
		}

		&.btn-secondary {
			background-color: #6c757d;
			color: white;
		}

		&:active {
			opacity: 0.8;
		}
	}
}
</style>
