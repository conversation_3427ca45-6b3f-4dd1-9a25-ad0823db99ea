<template>
	<view class="report-work-page">
		<!-- 工单信息展示 -->
		<view class="work-order-info" v-if="workOrderData">
			<view class="info-header">
				<text class="header-title">工单信息</text>
			</view>
			<view class="info-content">
				<view class="info-item">
					<text class="label">生产单号：</text>
					<text class="value">{{ workOrderData.workNo || '暂无单号' }}</text>
				</view>
				<view class="info-item">
					<text class="label">产品名称：</text>
					<text class="value">{{ workOrderData.productName || '未知产品' }}</text>
				</view>
				<view class="info-item" v-if="workOrderData.productCode">
					<text class="label">产品编码：</text>
					<text class="value">{{ workOrderData.productCode }}</text>
				</view>
				<view class="info-item">
					<text class="label">计划数量：</text>
					<text class="value">{{ formatQuantity(workOrderData.scheduleQuantity) }} {{ getUnitText(workOrderData.orderUnit) }}</text>
				</view>
				<view class="info-item" v-if="workOrderData.deliverDate">
					<text class="label">交期：</text>
					<text class="value">{{ formatDate(workOrderData.deliverDate) }}</text>
				</view>
			</view>
		</view>

		<!-- 报工表单 -->
		<view class="report-form">
			<view class="form-header">
				<text class="header-title">报工信息</text>
			</view>
			<view class="form-content">
				<!-- 任务类型 -->
				<view class="form-item">
					<text class="form-label required">任务类型：</text>
					<picker mode="selector" :value="typeIndex" :range="typeOptions" range-key="label" @change="handleTypeChange">
						<view class="picker-input">
							<text class="picker-text" :class="{ placeholder: typeIndex === -1 }">
								{{ typeIndex !== -1 ? typeOptions[typeIndex].label : '请选择任务类型' }}
							</text>
							<uni-icons type="arrowdown" size="14" color="#999"></uni-icons>
						</view>
					</picker>
				</view>

				<!-- 开始时间 -->
				<view class="form-item">
					<text class="form-label required">开始时间：</text>
					<picker mode="multiSelector" :value="startTimeArray" :range="timePickerRange" @change="handleStartTimeChange" @columnchange="handleStartTimeColumnChange">
						<view class="picker-input">
							<text class="picker-text" :class="{ placeholder: !formData.startTime }">
								{{ formData.startTime ? formatDateTime(formData.startTime) : '请选择开始时间' }}
							</text>
							<uni-icons type="arrowdown" size="14" color="#999"></uni-icons>
						</view>
					</picker>
				</view>

				<!-- 结束时间 -->
				<view class="form-item">
					<text class="form-label required">结束时间：</text>
					<picker mode="multiSelector" :value="endTimeArray" :range="timePickerRange" @change="handleEndTimeChange" @columnchange="handleEndTimeColumnChange">
						<view class="picker-input">
							<text class="picker-text" :class="{ placeholder: !formData.endTime }">
								{{ formData.endTime ? formatDateTime(formData.endTime) : '请选择结束时间' }}
							</text>
							<uni-icons type="arrowdown" size="14" color="#999"></uni-icons>
						</view>
					</picker>
				</view>

				<!-- 用时显示 -->
				<view class="form-item" v-if="formData.costTime > 0">
					<text class="form-label">用时：</text>
					<view class="duration-display">
						<text class="duration-text">{{ formatDuration(formData.costTime) }}</text>
					</view>
				</view>

				<!-- 人数 -->
				<view class="form-item">
					<text class="form-label">人数：</text>
					<input
						class="form-input"
						type="number"
						v-model="formData.costHeadcount"
						placeholder="请输入人数"
					/>
				</view>

				<!-- 生产线 -->
				<view class="form-item">
					<text class="form-label">生产线：</text>
					<picker mode="selector" :value="lineIndex" :range="lineOptions" range-key="label" @change="handleLineChange">
						<view class="picker-input">
							<text class="picker-text" :class="{ placeholder: lineIndex === -1 }">
								{{ lineIndex !== -1 ? lineOptions[lineIndex].label : '请选择生产线' }}
							</text>
							<uni-icons type="arrowdown" size="14" color="#999"></uni-icons>
						</view>
					</picker>
				</view>

				<!-- 数量 -->
				<view class="form-item">
					<text class="form-label required">数量：</text>
					<view class="input-with-unit">
						<input
							class="form-input flex-input"
							type="number"
							v-model="formData.quantity"
							placeholder="请输入数量"
						/>
						<text class="unit-text">{{ getUnitText(workOrderData?.orderUnit) }}</text>
					</view>
				</view>

				<!-- 件数 -->
				<view class="form-item">
					<text class="form-label">件数：</text>
					<input
						class="form-input"
						type="number"
						v-model="formData.piece"
						placeholder="请输入件数"
					/>
				</view>

				<!-- 批号 -->
				<view class="form-item">
					<text class="form-label">批号：</text>
					<input
						class="form-input"
						type="text"
						v-model="formData.batchNo"
						placeholder="请输入批号"
					/>
				</view>

				<!-- 温度 -->
				<view class="form-item">
					<text class="form-label">温度：</text>
					<input
						class="form-input"
						type="number"
						v-model="formData.temperature"
						placeholder="请输入温度"
					/>
				</view>

				<!-- 湿度 -->
				<view class="form-item">
					<text class="form-label">湿度：</text>
					<input
						class="form-input"
						type="number"
						v-model="formData.humidity"
						placeholder="请输入湿度"
					/>
				</view>

				<!-- 备注 -->
				<view class="form-item">
					<text class="form-label">备注：</text>
					<textarea
						class="form-textarea"
						v-model="formData.remark"
						placeholder="请输入备注"
					/>
				</view>
			</view>
		</view>

		<!-- 操作按钮 -->
		<view class="action-buttons">
			<button class="btn btn-primary" @click="submitReport" :disabled="submitting">
				{{ submitting ? '提交中...' : '提交报工' }}
			</button>
			<button class="btn btn-secondary" @click="goBack">返回</button>
		</view>
	</view>
</template>

<script>
import { getDictLabel, getBatchDictOptions, DICT_TYPE } from '../../../../../utils/dict';
import { getUnitPageApi } from '../../../../../api/scm/base/unit';
import { createReportOrderApi } from '../../../../../api/scm/mfg/reportorder';

export default {
	name: 'ReportWork',
	data() {
		return {
			// 从上一页面接收的工单数据
			workOrderData: null,
			// 操作类型
			operationType: '',
			// 接收数据的时间戳
			timestamp: null,
			// 提交状态
			submitting: false,

			// 表单数据
			formData: {
				workId: null,
				workNo: '',
				type: '',
				reportCode: '',
				startTime: null,
				endTime: null,
				costTime: 0,
				costHeadcount: null,
				line: '',
				quantity: null,
				piece: null,
				batchNo: '',
				temperature: null,
				humidity: null,
				remark: ''
			},

			// 字典数据
			dictOptions: {
				mfg_work_type: [],      // 生产任务类型
				manufacture_line: []    // 生产线
			},

			// 单位数据
			unitList: [],
			unitMap: new Map(),

			// 选择器相关
			typeIndex: -1,
			typeOptions: [],
			lineIndex: -1,
			lineOptions: [],

			// 时间选择器相关
			startTimeArray: [0, 0, 0, 0, 0],
			endTimeArray: [0, 0, 0, 0, 0],
			timePickerRange: []
		}
	},

	async onLoad() {
		// 初始化数据
		await this.initDictData();
		await this.loadUnits();
		this.initTimePickerRange();

		// 获取页面间通信的 eventChannel
		const eventChannel = this.getOpenerEventChannel();

		// 监听来自上一页面的数据
		eventChannel.on('acceptDataFormOpener', (data) => {
			console.log('报工页面接收到数据:', data);

			// 保存接收到的数据
			this.workOrderData = data.workOrderData;
			this.operationType = data.operationType;
			this.timestamp = data.timestamp;

			// 验证并初始化表单数据
			this.validateReceivedData();
			this.initFormData();
		});
	},

	methods: {
		// 初始化字典数据
		async initDictData() {
			try {
				const dictTypes = [
					DICT_TYPE.MFG_WORK_TYPE,      // 生产任务类型
					DICT_TYPE.MANUFACTURE_LINE    // 生产线
				];
				const dictMap = await getBatchDictOptions(dictTypes);

				this.dictOptions = {
					mfg_work_type: dictMap[DICT_TYPE.MFG_WORK_TYPE] || [],
					manufacture_line: dictMap[DICT_TYPE.MANUFACTURE_LINE] || []
				};

				// 设置选择器选项
				this.typeOptions = this.dictOptions.mfg_work_type;
				this.lineOptions = this.dictOptions.manufacture_line;

			} catch (error) {
				console.error('获取字典数据失败:', error);
				this.dictOptions = {
					mfg_work_type: [],
					manufacture_line: []
				};
				this.typeOptions = [];
				this.lineOptions = [];
			}
		},

		// 加载单位数据
		async loadUnits() {
			try {
				const response = await getUnitPageApi({
					pageNo: 1,
					pageSize: 100
				});

				if (response.code === 0 && response.data && response.data.list) {
					const units = response.data.list;
					this.unitList = units;
					// 建立单位映射
					units.forEach(unit => {
						if (unit && unit.id && unit.name) {
							this.unitMap.set(unit.id, unit.name);
						}
					});
				}
			} catch (error) {
				console.error('加载单位数据失败:', error);
				this.unitList = [];
				this.unitMap = new Map();
			}
		},

		// 验证接收到的数据
		validateReceivedData() {
			if (!this.workOrderData) {
				uni.showToast({
					title: '未获取到工单数据',
					icon: 'none'
				});
				return;
			}

			console.log('工单数据验证通过:', this.workOrderData);
		},

		// 初始化表单数据
		initFormData() {
			if (!this.workOrderData) return;

			const workOrder = this.workOrderData;

			// 填充基础信息
			this.formData.workId = workOrder.id;
			this.formData.workNo = workOrder.workNo;
			this.formData.type = '2'; // 默认生产类型
			this.formData.line = workOrder.scheduleLine || workOrder.actualLine || '';
			this.formData.quantity = workOrder.scheduleQuantity;
			this.formData.piece = workOrder.schedulePiece;
			this.formData.costHeadcount = workOrder.scheduleHeadcount;

			// 生成批号（当前年月日）
			const now = new Date();
			const year = now.getFullYear();
			const month = String(now.getMonth() + 1).padStart(2, '0');
			const day = String(now.getDate()).padStart(2, '0');
			this.formData.batchNo = `${year}${month}${day}`;

			// 设置选择器索引
			this.setTypeIndex();
			this.setLineIndex();
		},

	// 设置任务类型选择器索引
	setTypeIndex() {
		if (this.formData.type && this.typeOptions.length > 0) {
			const index = this.typeOptions.findIndex(item => item.value === this.formData.type);
			this.typeIndex = index !== -1 ? index : -1;
		}
	},

	// 设置生产线选择器索引
	setLineIndex() {
		if (this.formData.line && this.lineOptions.length > 0) {
			const index = this.lineOptions.findIndex(item => item.value === this.formData.line);
			this.lineIndex = index !== -1 ? index : -1;
		}
	},

	// 初始化时间选择器范围
	initTimePickerRange() {
		const now = new Date();
		const currentYear = now.getFullYear();

		// 年份范围（当前年份前后2年）
		const years = [];
		for (let i = currentYear - 2; i <= currentYear + 2; i++) {
			years.push(String(i));
		}

		// 月份范围
		const months = [];
		for (let i = 1; i <= 12; i++) {
			months.push(String(i).padStart(2, '0'));
		}

		// 日期范围
		const days = [];
		for (let i = 1; i <= 31; i++) {
			days.push(String(i).padStart(2, '0'));
		}

		// 小时范围
		const hours = [];
		for (let i = 0; i <= 23; i++) {
			hours.push(String(i).padStart(2, '0'));
		}

		// 分钟范围
		const minutes = [];
		for (let i = 0; i <= 59; i++) {
			minutes.push(String(i).padStart(2, '0'));
		}

		this.timePickerRange = [years, months, days, hours, minutes];
	},

	// 处理任务类型选择
	handleTypeChange(e) {
		const index = e.detail.value;
		this.typeIndex = index;
		if (index >= 0 && index < this.typeOptions.length) {
			this.formData.type = this.typeOptions[index].value;
		}
	},

	// 处理生产线选择
	handleLineChange(e) {
		const index = e.detail.value;
		this.lineIndex = index;
		if (index >= 0 && index < this.lineOptions.length) {
			this.formData.line = this.lineOptions[index].value;
		}
	},

	// 处理开始时间选择
	handleStartTimeChange(e) {
		const values = e.detail.value;
		this.startTimeArray = values;

		const year = this.timePickerRange[0][values[0]];
		const month = this.timePickerRange[1][values[1]];
		const day = this.timePickerRange[2][values[2]];
		const hour = this.timePickerRange[3][values[3]];
		const minute = this.timePickerRange[4][values[4]];

		const dateTime = new Date(`${year}-${month}-${day} ${hour}:${minute}:00`);
		this.formData.startTime = dateTime.getTime();

		// 重新计算用时
		this.calculateCostTime();
	},

	// 处理开始时间列变化
	handleStartTimeColumnChange(e) {
		// 可以在这里处理联动逻辑，比如月份变化时更新日期范围
	},

	// 处理结束时间选择
	handleEndTimeChange(e) {
		const values = e.detail.value;
		this.endTimeArray = values;

		const year = this.timePickerRange[0][values[0]];
		const month = this.timePickerRange[1][values[1]];
		const day = this.timePickerRange[2][values[2]];
		const hour = this.timePickerRange[3][values[3]];
		const minute = this.timePickerRange[4][values[4]];

		const dateTime = new Date(`${year}-${month}-${day} ${hour}:${minute}:00`);
		this.formData.endTime = dateTime.getTime();

		// 重新计算用时
		this.calculateCostTime();
	},

	// 处理结束时间列变化
	handleEndTimeColumnChange(e) {
		// 可以在这里处理联动逻辑
	},

	// 计算用时
	calculateCostTime() {
		if (this.formData.startTime && this.formData.endTime) {
			const timeDiff = this.formData.endTime - this.formData.startTime;
			if (timeDiff > 0) {
				// 转换为分钟
				this.formData.costTime = Math.round(timeDiff / (1000 * 60));
			} else {
				this.formData.costTime = 0;
			}
		} else {
			this.formData.costTime = 0;
		}
	},

	// 格式化用时显示
	formatDuration(minutes) {
		if (!minutes || minutes <= 0) return '0分钟';

		const hours = Math.floor(minutes / 60);
		const mins = minutes % 60;

		if (hours > 0) {
			return mins > 0 ? `${hours}小时${mins}分钟` : `${hours}小时`;
		} else {
			return `${mins}分钟`;
		}
	},

	// 格式化数量
	formatQuantity(value) {
		if (value === null || value === undefined || value === '') return '0';
		if (isNaN(value)) return '0';
		return Number(value).toLocaleString();
	},

	// 获取单位文本
	getUnitText(unit) {
		if (!unit) return '';
		// 使用单位映射获取单位名称
		const unitId = typeof unit === 'string' ? parseInt(unit) : unit;
		const unitName = this.unitMap.get(unitId);
		return unitName || unit.toString();
	},

	// 格式化日期
	formatDate(timestamp) {
		if (!timestamp) return '-';
		const date = new Date(timestamp);
		const year = date.getFullYear();
		const month = String(date.getMonth() + 1).padStart(2, '0');
		const day = String(date.getDate()).padStart(2, '0');
		return `${year}-${month}-${day}`;
	},

	// 格式化日期时间
	formatDateTime(timestamp) {
		if (!timestamp) return '-';
		const date = new Date(timestamp);
		const year = date.getFullYear();
		const month = String(date.getMonth() + 1).padStart(2, '0');
		const day = String(date.getDate()).padStart(2, '0');
		const hours = String(date.getHours()).padStart(2, '0');
		const minutes = String(date.getMinutes()).padStart(2, '0');
		return `${year}-${month}-${day} ${hours}:${minutes}`;
	},

	// 表单验证
	validateForm() {
		if (!this.formData.type) {
			uni.showToast({
				title: '请选择任务类型',
				icon: 'none'
			});
			return false;
		}

		if (!this.formData.startTime) {
			uni.showToast({
				title: '请选择开始时间',
				icon: 'none'
			});
			return false;
		}

		if (!this.formData.endTime) {
			uni.showToast({
				title: '请选择结束时间',
				icon: 'none'
			});
			return false;
		}

		if (this.formData.endTime <= this.formData.startTime) {
			uni.showToast({
				title: '结束时间不能早于或等于开始时间',
				icon: 'none'
			});
			return false;
		}

		if (!this.formData.quantity || this.formData.quantity <= 0) {
			uni.showToast({
				title: '请输入有效的数量',
				icon: 'none'
			});
			return false;
		}

		return true;
	},

	// 提交报工
	async submitReport() {
		// 表单验证
		if (!this.validateForm()) {
			return;
		}

		this.submitting = true;

		try {
			// 准备提交数据
			const submitData = {
				workId: this.formData.workId,
				workNo: this.formData.workNo,
				type: this.formData.type,
				startTime: this.formData.startTime,
				endTime: this.formData.endTime,
				costTime: this.formData.costTime.toString(),
				costHeadcount: Number(this.formData.costHeadcount) || 0,
				line: this.formData.line,
				quantity: Number(this.formData.quantity),
				piece: Number(this.formData.piece) || 0,
				batchNo: this.formData.batchNo,
				temperature: Number(this.formData.temperature) || 0,
				humidity: Number(this.formData.humidity) || 0,
				remark: this.formData.remark || '',
				slotNo: 0 // 默认值
			};

			console.log('提交报工数据:', submitData);

			// 调用API提交
			const response = await createReportOrderApi(submitData);

			if (response.code === 0) {
				uni.showToast({
					title: '报工提交成功',
					icon: 'success'
				});

				// 延迟返回上一页
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
			} else {
				uni.showToast({
					title: response.msg || '提交失败',
					icon: 'none'
				});
			}

		} catch (error) {
			console.error('提交报工失败:', error);
			uni.showToast({
				title: '提交失败，请重试',
				icon: 'none'
			});
		} finally {
			this.submitting = false;
		}
	},

	// 返回上一页
	goBack() {
		uni.navigateBack();
	}
}
}
</script>

<style lang="scss" scoped>
.report-work-page {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

.work-order-info, .report-form {
	background-color: white;
	border-radius: 12rpx;
	margin-bottom: 20rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.info-header, .form-header {
	padding: 20rpx;
	background-color: #f8f9fa;
	border-bottom: 1px solid #e5e5e5;

	.header-title {
		font-size: 28rpx;
		font-weight: 600;
		color: #333;
	}
}

.info-content, .form-content {
	padding: 20rpx;
}

.info-item {
	display: flex;
	align-items: center;
	margin-bottom: 16rpx;

	&:last-child {
		margin-bottom: 0;
	}

	.label {
		font-size: 26rpx;
		color: #666;
		width: 160rpx;
		flex-shrink: 0;
	}

	.value {
		font-size: 26rpx;
		color: #333;
		flex: 1;
		word-break: break-all;
	}
}

.form-item {
	margin-bottom: 24rpx;

	&:last-child {
		margin-bottom: 0;
	}

	.form-label {
		display: block;
		font-size: 26rpx;
		color: #333;
		margin-bottom: 12rpx;

		&.required::after {
			content: '*';
			color: #ff4757;
			margin-left: 4rpx;
		}
	}

	.form-input, .form-textarea {
		width: 100%;
		padding: 16rpx;
		border: 1px solid #ddd;
		border-radius: 8rpx;
		font-size: 26rpx;
		box-sizing: border-box;
		background-color: #fff;

		&:focus {
			border-color: #007bff;
			outline: none;
		}

		&::placeholder {
			color: #999;
		}
	}

	.form-textarea {
		height: 120rpx;
		resize: none;
	}
}

// 选择器样式
.picker-input {
	display: flex;
	align-items: center;
	justify-content: space-between;
	width: 100%;
	padding: 16rpx;
	border: 1px solid #ddd;
	border-radius: 8rpx;
	background-color: #fff;
	box-sizing: border-box;

	.picker-text {
		font-size: 26rpx;
		color: #333;
		flex: 1;

		&.placeholder {
			color: #999;
		}
	}
}

// 用时显示样式
.duration-display {
	padding: 16rpx;
	background-color: #f8f9fa;
	border-radius: 8rpx;
	border: 1px solid #e5e5e5;

	.duration-text {
		font-size: 26rpx;
		color: #333;
		font-weight: 500;
	}
}

// 带单位的输入框
.input-with-unit {
	display: flex;
	align-items: center;

	.flex-input {
		flex: 1;
		margin-right: 12rpx;
	}

	.unit-text {
		font-size: 24rpx;
		color: #666;
		padding: 16rpx 12rpx;
		background-color: #f8f9fa;
		border: 1px solid #ddd;
		border-radius: 8rpx;
		min-width: 80rpx;
		text-align: center;
	}
}

.action-buttons {
	padding: 20rpx 0;

	.btn {
		width: 100%;
		height: 88rpx;
		border-radius: 8rpx;
		font-size: 28rpx;
		margin-bottom: 16rpx;
		border: none;
		display: flex;
		align-items: center;
		justify-content: center;

		&:last-child {
			margin-bottom: 0;
		}

		&.btn-primary {
			background-color: #007bff;
			color: white;

			&:disabled {
				background-color: #6c757d;
				opacity: 0.6;
			}
		}

		&.btn-secondary {
			background-color: #6c757d;
			color: white;
		}

		&:active:not(:disabled) {
			opacity: 0.8;
		}
	}
}

// 响应式设计
@media (max-width: 750rpx) {
	.info-item {
		flex-direction: column;
		align-items: flex-start;

		.label {
			width: 100%;
			margin-bottom: 8rpx;
		}
	}

	.input-with-unit {
		flex-direction: column;

		.flex-input {
			margin-right: 0;
			margin-bottom: 12rpx;
		}

		.unit-text {
			width: 100%;
		}
	}
}
</style>
