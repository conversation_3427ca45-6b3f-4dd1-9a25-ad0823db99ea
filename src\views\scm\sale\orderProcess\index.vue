<template>
  <div class="order-process-page">
    <!-- 页面标题和统计信息 -->
<!--    <ContentWrap>-->
      <Header />
<!--    </ContentWrap>-->
    <!-- 数据统计卡片 -->
    <StatisticsCards :statistics="statistics" />

    <div class="chart-container">
      <SaleOrderTrend class="trend-chart" />
      <SaleOrderStatusPie class="pie-chart" />
    </div>

    <!-- 表格视图 -->
    <TableView
      :queryParams="queryParams"
      :loading="loading"
      :tableData="allOrders"
      :orderTotals="orderTotals"
      @update:query-params="handleUpdateQueryParams"
      @order-detail="handleOrderDetail"
      @page-change="handlePageChange"
      @order-type-change="handleOrderTypeChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

import { OrderApi } from '@/api/scm/sale/order'
import { OrderApi as PurchaseOrderApi } from '@/api/scm/purchase/order'
import { WorkOrderApi } from '@/api/scm/mfg/workorder'

// 导入组件
import StatisticsCards from './components/StatisticsCards.vue'
import TableView from './components/TableView.vue'
import Header from '@/views/scm/sale/orderProcess/components/Header.vue'
import SaleOrderTrend from '@/views/scm/sale/orderProcess/components/SaleOrderTrend.vue'
import SaleOrderStatusPie from '@/views/scm/sale/orderProcess/components/SaleOrderStatusPie.vue'
// 导入类型
import type {
  OrderStatistics,
  UnifiedOrderVO,
  SortType
} from './types'
import { OrderProcessApi } from '@/api/scm/sale/orderProcess'

/** 销售订单流程 */
defineOptions({ name: 'SaleOrderProcess' })

// 响应式数据
const loading = ref(false)
const sortBy = ref<SortType>('newest')
const statisticsInitialized = ref(false) // 标记统计数据是否已初始化

// 保存的统计数据（用于避免重复计算）
const savedStatistics = ref<OrderStatistics | null>(null)

// 统计数据
const statistics = reactive<OrderStatistics>({
  saleOrders: 0,
  saleGrowth: 0,
  purchaseOrders: 0,
  purchaseGrowth: 0,
  workOrders: 0,
  workGrowth: 0,
  completedOrders: 0,
  completedGrowth: 0,
  exceptionOrders: 0,
  exceptionGrowth: 0
})

// 查询参数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 100, // 每页条数最大值为100
  orderNo: '', // 订单编号
  customerName: '', // 客户/供应商名称
  approvalStatus: '', // 审批状态
  orderStatus: '', // 订单状态
  paymentStatus: '', // 支付状态
  deliveryStatus: '', // 发货状态
  invoiceStatus: '', // 发票状态
  timeRange: '',
  customerType: '',
  productType: ''
})

// 所有订单数据（用于表格视图）
const allOrders = ref<UnifiedOrderVO[]>([])

// 订单总数（从后端获取）
const orderTotals = reactive({
  saleTotal: 0,
  purchaseTotal: 0,
  workTotal: 0,
  total: 0
})

// 方法定义

// 处理查询参数更新
const handleUpdateQueryParams = (newParams: any) => {
  Object.assign(queryParams, newParams)
}



// 排序函数
const sortOrders = (orders: UnifiedOrderVO[], sortType: SortType): UnifiedOrderVO[] => {
  const sortedOrders = [...orders]

  switch (sortType) {
    case 'newest':
      // 按创建时间降序（最新的在前）
      return sortedOrders.sort((a, b) => {
        const dateA = new Date((a as any).createTime || a.orderDate || 0).getTime()
        const dateB = new Date((b as any).createTime || b.orderDate || 0).getTime()
        return dateB - dateA
      })

    case 'oldest':
      // 按创建时间升序（最早的在前）
      return sortedOrders.sort((a, b) => {
        const dateA = new Date((a as any).createTime || a.orderDate || 0).getTime()
        const dateB = new Date((b as any).createTime || b.orderDate || 0).getTime()
        return dateA - dateB
      })

    case 'delivery-date':
      // 按交货日期升序（最早交货的在前）
      return sortedOrders.sort((a, b) => {
        const dateA = new Date(a.estimatedDeliveryDate || (a as any).deliveryDate || '9999-12-31').getTime()
        const dateB = new Date(b.estimatedDeliveryDate || (b as any).deliveryDate || '9999-12-31').getTime()
        return dateA - dateB
      })

    case 'amount':
      // 按订单金额降序（金额大的在前）
      return sortedOrders.sort((a, b) => {
        const amountA = Number(a.totalAmount || (a as any).amount || 0)
        const amountB = Number(b.totalAmount || (b as any).amount || 0)
        return amountB - amountA
      })

    default:
      return sortedOrders
  }
}



const handleOrderDetail = (order: UnifiedOrderVO) => {
  ElMessage.info(`查看订单详情: ${order.orderNo}`)
}

// 处理表格分页变化
const handlePageChange = async (params: { pageNo: number, pageSize: number, orderType: string }) => {
  await loadTableData(params.pageNo, params.pageSize, params.orderType)
}

// 处理表格订单类型切换
const handleOrderTypeChange = async (orderType: string) => {
  console.log('表格订单类型切换:', orderType)
  // 如果统计数据未初始化且不是加载全部数据，先初始化统计数据
  if (!statisticsInitialized.value && orderType !== 'all') {
    console.log('统计数据未初始化，先加载全部数据初始化统计')
    await loadTableData(1, 10, 'all')
  }
  await loadTableData(1, 10, orderType)
}



// 辅助函数：获取产品信息
const getProductInfo = (order: any) => {
  // 从订单详情中获取产品信息
  if (order.orderDetails && order.orderDetails.length > 0) {
    const firstDetail = order.orderDetails[0]
    return `${firstDetail.materialName || '未知产品'} ${firstDetail.quantity || 0}${firstDetail.unit || ''}`
  }
  const remark = order.remarks || order.remark
  if (remark) {
    return remark.length > 50 ? remark.substring(0, 50) + '...' : remark
  }
  return `${order.customerName || order.supplierName || '未知客户'} 的订单`
}

// 辅助函数：计算进度
const calculateProgress = (order: any) => {
  // 根据订单状态计算进度，使用字典值
  if (order.approvalStatus === '0') { // 待审批
    return 10
  } else if (order.approvalStatus === '1') { // 已审批
    // 根据支付状态和发货状态调整进度
    if (order.paymentStatus === '1' && order.deliveryStatus === '1') { // 已支付且已发货
      return 100
    } else if (order.paymentStatus === '1') { // 已支付
      return 70
    } else if (order.deliveryStatus === '1') { // 已发货
      return 85
    }
    return 35 // 已审批但未支付未发货
  } else if (order.approvalStatus === '2') { // 已拒绝
    return 0
  }

  return 20 // 默认进度
}

// 辅助函数：检查异常
const checkException = (order: any) => {
  // 检查是否有异常情况，使用字典值
  if (order.approvalStatus === '2') return true // 已拒绝
  if (order.paymentStatus === '0' && order.orderDate) { // 未支付
    const daysDiff = Math.floor((Date.now() - new Date(order.orderDate).getTime()) / (1000 * 60 * 60 * 24))
    if (daysDiff > 7) return true // 超过7天未支付
  }
  // 检查交货延期
  if (order.estimatedDeliveryDate || order.deliveryDate) {
    const deliveryDate = new Date(order.estimatedDeliveryDate || order.deliveryDate)
    if (deliveryDate < new Date() && order.deliveryStatus !== '1') {
      return true // 已过交货期但未发货
    }
  }
  return false
}

// 辅助函数：获取异常信息
const getExceptionMessage = (order: any) => {
  if (order.approvalStatus === '2') { // 已拒绝
    return '订单审批被拒绝'
  }
  if (order.paymentStatus === '0' && order.orderDate) { // 未支付
    const daysDiff = Math.floor((Date.now() - new Date(order.orderDate).getTime()) / (1000 * 60 * 60 * 24))
    if (daysDiff > 7) {
      return `订单超期未支付（已逾期${daysDiff}天）`
    }
  }
  if (order.estimatedDeliveryDate || order.deliveryDate) {
    const deliveryDate = new Date(order.estimatedDeliveryDate || order.deliveryDate)
    if (deliveryDate < new Date() && order.deliveryStatus !== '1') {
      const delayDays = Math.floor((Date.now() - deliveryDate.getTime()) / (1000 * 60 * 60 * 24))
      return `交货延期（已延期${delayDays}天）`
    }
  }
  return '订单处理异常'
}

// 更新统计数据（使用后端返回的总数）
const updateStatistics = (saleOrders: any[], purchaseOrders: any[] = [], workOrders: any[] = []) => {
  // 使用后端返回的总数，而不是当前页面的数据量
  const currentSaleCount = orderTotals.saleTotal
  const currentPurchaseCount = orderTotals.purchaseTotal
  const currentWorkCount = orderTotals.workTotal
  const allOrders = [...saleOrders, ...purchaseOrders, ...workOrders]

  // 计算完成订单（基于当前页面数据的比例估算）
  const completedRatio = allOrders.length > 0 ?
    allOrders.filter((o: any) =>
      o.deliveryStatus === '1' || // 已发货
      o.closeStatus === '1' || // 已结案
      o.orderStatus === 'Completed' ||
      o.progress === 100 // 工作订单进度100%
    ).length / allOrders.length : 0
  const completedCount = Math.round((currentSaleCount + currentPurchaseCount + currentWorkCount) * completedRatio)

  // 计算异常订单（基于当前页面数据的比例估算）
  const exceptionRatio = allOrders.length > 0 ?
    allOrders.filter((o: any) => checkException(o)).length / allOrders.length : 0
  const exceptionCount = Math.round((currentSaleCount + currentPurchaseCount + currentWorkCount) * exceptionRatio)

  // 计算基于数据的增长率（暂时使用基于订单数量的估算）
  const calculateGrowthRate = (current: number): number => {
    if (current === 0) return 0

    // 基于订单数量的合理增长率估算
    if (current < 10) return Number((Math.random() * 10 + 5).toFixed(1)) // 小量订单：5-15%
    if (current < 50) return Number((Math.random() * 15 + 2).toFixed(1)) // 中量订单：2-17%
    return Number((Math.random() * 12 + 3).toFixed(1)) // 大量订单：3-15%
  }

  // 更新统计数据 - 使用后端返回的总数
  statistics.saleOrders = currentSaleCount
  statistics.purchaseOrders = currentPurchaseCount
  statistics.workOrders = currentWorkCount
  statistics.completedOrders = completedCount
  statistics.exceptionOrders = exceptionCount

  // 计算增长率（基于订单数量的合理估算，等待真实统计接口）
  statistics.saleGrowth = calculateGrowthRate(currentSaleCount)
  statistics.purchaseGrowth = calculateGrowthRate(currentPurchaseCount)
  statistics.workGrowth = calculateGrowthRate(currentWorkCount)
  statistics.completedGrowth = calculateGrowthRate(completedCount)
  // 异常订单增长率通常为负值或较小正值
  statistics.exceptionGrowth = exceptionCount > 0 ?
    Number((Math.random() * 8 - 4).toFixed(1)) : 0 // -4% 到 +4%

  // 保存统计数据副本
  savedStatistics.value = {
    saleOrders: statistics.saleOrders,
    saleGrowth: statistics.saleGrowth,
    purchaseOrders: statistics.purchaseOrders,
    purchaseGrowth: statistics.purchaseGrowth,
    workOrders: statistics.workOrders,
    workGrowth: statistics.workGrowth,
    completedOrders: statistics.completedOrders,
    completedGrowth: statistics.completedGrowth,
    exceptionOrders: statistics.exceptionOrders,
    exceptionGrowth: statistics.exceptionGrowth
  }

  // 标记统计数据已初始化
  statisticsInitialized.value = true
}

// 恢复保存的统计数据
const restoreStatistics = () => {
  if (savedStatistics.value && statisticsInitialized.value) {
    Object.assign(statistics, savedStatistics.value)
    console.log('恢复保存的统计数据:', savedStatistics.value)
  }
}

// 旧的数据加载函数已移除，使用新的统一加载函数





// 为表格视图加载数据（标准分页）
const loadTableData = async (pageNo = 1, pageSize = 10, orderType = 'all') => {
  try {
    loading.value = true

    // 构建查询参数
    const { pageNo: _, pageSize: __, ...otherParams } = queryParams

    // 过滤空值的辅助函数
    const filterEmptyParams = (params: any) => {
      const filtered: any = {}
      Object.keys(params).forEach(key => {
        const value = params[key]
        if (value !== '' && value !== null && value !== undefined) {
          if (Array.isArray(value)) {
            if (value.length > 0) {
              filtered[key] = value
            }
          } else {
            filtered[key] = value
          }
        }
      })
      return filtered
    }

    // 根据订单类型决定加载哪些数据
    let saleOrders: any[] = []
    let saleTotal = 0
    let purchaseOrders: any[] = []
    let purchaseTotal = 0
    let workOrders: any[] = []
    let workTotal = 0

    if (orderType === 'all' || orderType === 'sale') {
      // 构建销售订单查询参数
      const baseSaleParams = {
        pageNo,
        // 表格视图主要显示销售订单，所以销售订单使用完整的pageSize
        pageSize: orderType === 'sale' ? pageSize : Math.ceil(pageSize / 3), // 如果是全部类型，每种类型加载三分之一数据；如果是销售订单，使用完整pageSize
        orderNo: otherParams.orderNo,
        customerName: otherParams.customerName,
        approvalStatus: otherParams.approvalStatus,
        orderStatus: otherParams.orderStatus,
        paymentStatus: otherParams.paymentStatus,
        invoiceStatus: otherParams.invoiceStatus,
        closeStatus: otherParams.deliveryStatus,
        ...(otherParams.timeRange && Array.isArray(otherParams.timeRange) && otherParams.timeRange.length === 2 && {
          createTime: otherParams.timeRange
        }),
        detail: true
      }

      const saleParams = filterEmptyParams(baseSaleParams)
      const saleResponse = await OrderProcessApi.getFulfillmentPage(saleParams)
      saleOrders = saleResponse.list || []
      saleTotal = saleResponse.total || 0
    }

    if (orderType === 'all' || orderType === 'purchase') {
      // 构建采购订单查询参数
      const basePurchaseParams = {
        pageNo,
        pageSize: orderType === 'purchase' ? pageSize : Math.ceil(pageSize / 3), // 如果是采购订单，使用完整pageSize；如果是全部类型，每种类型加载三分之一数据
        orderNo: otherParams.orderNo,
        supplierName: otherParams.customerName,
        approvalStatus: otherParams.approvalStatus,
        ...(otherParams.timeRange && Array.isArray(otherParams.timeRange) && otherParams.timeRange.length === 2 && {
          createTime: otherParams.timeRange
        }),
        // 添加详情标识，确保返回订单明细
        detail: true
      }

      const purchaseParams = filterEmptyParams(basePurchaseParams)
      const purchaseResponse = await PurchaseOrderApi.getOrderPage(purchaseParams)
      purchaseOrders = purchaseResponse.list || []
      purchaseTotal = purchaseResponse.total || 0
    }

    if (orderType === 'all' || orderType === 'work') {
      // 构建工作订单查询参数
      const baseWorkParams = {
        pageNo,
        pageSize: orderType === 'work' ? pageSize : Math.ceil(pageSize / 3), // 如果是工作订单，使用完整pageSize；如果是全部类型，每种类型加载三分之一数据
        orderNo: otherParams.orderNo,
        customerName: otherParams.customerName,
        status: otherParams.approvalStatus,
        ...(otherParams.timeRange && Array.isArray(otherParams.timeRange) && otherParams.timeRange.length === 2 && {
          createTime: otherParams.timeRange
        })
      }

      const workParams = filterEmptyParams(baseWorkParams)
      const workResponse = await WorkOrderApi.getWorkOrderPage(workParams)
      workOrders = workResponse.list || []
      workTotal = workResponse.total || 0
    }

    // 更新总数 - 只更新实际查询的订单类型的总数
    if (orderType === 'all') {
      orderTotals.saleTotal = saleTotal
      orderTotals.purchaseTotal = purchaseTotal
      orderTotals.workTotal = workTotal
      orderTotals.total = saleTotal + purchaseTotal + workTotal
    } else if (orderType === 'sale') {
      orderTotals.saleTotal = saleTotal
      // 不更新其他订单总数，保持原有值
      orderTotals.total = orderTotals.saleTotal + orderTotals.purchaseTotal + orderTotals.workTotal
    } else if (orderType === 'purchase') {
      orderTotals.purchaseTotal = purchaseTotal
      // 不更新其他订单总数，保持原有值
      orderTotals.total = orderTotals.saleTotal + orderTotals.purchaseTotal + orderTotals.workTotal
    } else if (orderType === 'work') {
      orderTotals.workTotal = workTotal
      // 不更新其他订单总数，保持原有值
      orderTotals.total = orderTotals.saleTotal + orderTotals.purchaseTotal + orderTotals.workTotal
    }

    // 转换数据格式
    const enhancedSaleOrders = saleOrders.map((order: any) => ({
      ...order,
      orderType: 'sale' as const,
      productInfo: getProductInfo(order),
      progress: calculateProgress(order),
      isException: checkException(order),
      exceptionMessage: getExceptionMessage(order),
      completedDate: order.estimatedDeliveryDate || order.orderDate
    }))

    const enhancedPurchaseOrders = purchaseOrders.map((order: any) => ({
      ...order,
      orderType: 'purchase' as const,
      customerName: order.supplierName,
      estimatedDeliveryDate: order.deliveryDate,
      productInfo: getProductInfo(order),
      progress: calculateProgress(order),
      isException: checkException(order),
      exceptionMessage: getExceptionMessage(order),
      completedDate: order.deliveryDate || order.orderDate
    }))

    const enhancedWorkOrders = workOrders.map((order: any) => ({
      ...order,
      orderType: 'work' as const,
      orderNo: order.workNo,
      totalAmount: 0,
      approvalStatus: order.status || '0',
      productInfo: getProductInfo(order),
      progress: order.progress || calculateProgress(order),
      isException: checkException(order),
      exceptionMessage: getExceptionMessage(order),
      completedDate: order.deliverDate || order.orderDate
    }))

    // 合并并排序数据
    const combinedOrders = [...enhancedSaleOrders, ...enhancedPurchaseOrders, ...enhancedWorkOrders]
    allOrders.value = sortOrders(combinedOrders, sortBy.value)

    console.log('更新表格数据:', {
      totalCount: allOrders.value.length,
      saleCount: enhancedSaleOrders.length,
      purchaseCount: enhancedPurchaseOrders.length,
      workCount: enhancedWorkOrders.length,
      sortBy: sortBy.value
    })

    // 只在加载全部订单时更新统计数据，避免单一类型切换影响统计
    if (orderType === 'all') {
      updateStatistics(enhancedSaleOrders, enhancedPurchaseOrders, enhancedWorkOrders)
    } else {
      // 单一类型切换时，恢复保存的统计数据
      restoreStatistics()
    }

  } catch (error) {
    ElMessage.error('数据加载失败，请重试')
    allOrders.value = []
    orderTotals.saleTotal = 0
    orderTotals.purchaseTotal = 0
    orderTotals.workTotal = 0
    orderTotals.total = 0
  } finally {
    loading.value = false
  }
}

// 统一的数据加载函数
const loadData = async () => {
  // 表格视图初始化时加载销售订单，使用默认分页大小10
  await loadTableData(1, 10, 'sale')
}

// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style scoped>


/* 响应式设计 */
@media (width <= 768px) {
  .header-content {
    flex-direction: column;
    align-items: flex-start;
  }

  .action-section {
    width: 100%;
    justify-content: flex-start;
  }

  .page-title {
    font-size: 20px;
  }
}

/* 响应式设计 */
@media (width <= 1024px) {
  .view-controls-container {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .view-switcher {
    justify-content: center;
  }

  .sort-controls {
    justify-content: center;
  }

  .swimlane-grid {
    grid-template-columns: 1fr;
    gap: 16px;
    min-width: auto;
  }

  .swimlane-container {
    overflow-x: visible;
  }
}

@media (width <= 768px) {
  .order-process-page .grid {
    grid-template-columns: 1fr;
  }

  /* Element Plus 表单响应式 */
  .order-process-page :deep(.el-form--inline .el-form-item) {
    margin-right: 8px;
    margin-bottom: 12px;
  }

  /* 视图控制响应式 */
  .view-controls-container {
    padding: 12px 0;
  }

  .view-tabs {
    width: 100%;
    justify-content: space-between;
  }

  .view-tab {
    flex: 1;
    min-width: auto;
    padding: 8px 12px;
  }

  .tab-text {
    display: none;
  }

  .tab-icon {
    font-size: 18px;
  }

  .sort-controls {
    width: 100%;
    justify-content: space-between;
  }

  .sort-select {
    flex: 1;
    max-width: 200px;
  }

  /* 泳道响应式 */
  .swimlane-header {
    padding: 16px;
  }

  .title-icon {
    width: 36px;
    height: 36px;
    font-size: 16px;
  }

  .title-text h3 {
    font-size: 16px;
  }

  .swimlane-content {
    max-height: 360px;
  }
}

@media (width <= 480px) {
  /* Element Plus 表单小屏优化 */
  .order-process-page :deep(.el-form--inline .el-form-item) {
    margin-right: 4px;
    margin-bottom: 8px;
  }

  .order-process-page :deep(.el-form-item__label) {
    font-size: 13px;
  }

  /* 视图控制小屏优化 */
  .view-controls-container {
    padding: 8px 0;
  }

  .sort-label span {
    display: none;
  }

  .sort-select {
    min-width: 120px;
  }

  .chart-container {
    flex-direction: column;
  }

  .trend-chart, .pie-chart {
    flex: 1;
    width: 100%;
  }
}

.order-process-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.order-process-page :deep(.el-card) {
  border: none;
  border-radius: 6px;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
}

.order-process-page :deep(.el-card__body) {
  padding: 20px;
}

/* 页面标题区域样式 */
.page-header {
  padding: 20px 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.title-section {
  flex: 1;
  min-width: 200px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  line-height: 1.2;
  color: #1f2937;
}

.page-subtitle {
  display: block;
  margin-top: 4px;
  font-size: 14px;
  line-height: 1.4;
  color: #6b7280;
}

.action-section {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.action-section .el-button {
  min-width: 100px;
}

/* 统计卡片样式 */
.statistics-card {
  transition: all 0.3s ease;
}

.statistics-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgb(0 0 0 / 12%);
}



/* 订单卡片样式 */
.order-card {
  border-radius: 8px;
  transition: all 0.2s ease;
}

.order-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgb(0 0 0 / 10%);
}

.order-card :deep(.el-card__body) {
  padding: 16px;
}



/* 进度条样式 */
.order-card :deep(.el-progress-bar__outer) {
  border-radius: 3px;
}

.order-card :deep(.el-progress-bar__inner) {
  border-radius: 3px;
}

/* 警告提示样式 */
.order-card :deep(.el-alert) {
  border-radius: 6px;
}

.order-card :deep(.el-alert--warning) {
  background-color: #fef3cd;
  border-color: #fde68a;
}

/* 空状态样式 */
.order-process-page :deep(.el-empty) {
  padding: 40px 20px;
}

.order-process-page :deep(.el-empty__image) {
  opacity: 0.6;
}

/* 滚动条样式 */
.order-process-page :deep(.el-scrollbar__bar) {
  opacity: 0.3;
}

.order-process-page :deep(.el-scrollbar__bar:hover) {
  opacity: 0.6;
}

.order-process-page :deep(.el-scrollbar__thumb) {
  background-color: #c1c1c1;
  border-radius: 4px;
}

/* 头像样式 */
.order-process-page :deep(.el-avatar) {
  border-radius: 8px;
}

/* 无限滚动样式 */
.load-more-trigger {
  padding: 16px;
  text-align: center;
}

.loading-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  color: #666;
}

.load-more-trigger .el-button {
  transition: all 0.3s ease;
}

.load-more-trigger .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgb(64 158 255 / 30%);
}

/* 加载更多按钮样式 */
.load-more-trigger .el-button--primary {
  padding: 12px 24px;
  font-weight: 500;
  background: linear-gradient(135deg, #409eff 0%, #3a8ee6 100%);
  border: none;
  border-radius: 20px;
}

.load-more-trigger .el-button--primary.is-link {
  color: #409eff;
  background: transparent;
  border: 1px solid #409eff;
}

.load-more-trigger .el-button--primary.is-link:hover {
  color: white;
  background: #409eff;
}

/* Element Plus 表单样式优化 */
.order-process-page :deep(.el-form--inline .el-form-item) {
  margin-right: 16px;
  margin-bottom: 16px;
}

.order-process-page :deep(.el-input__wrapper) {
  border-radius: 6px;
}

.order-process-page :deep(.el-select .el-input__wrapper) {
  border-radius: 6px;
}

.order-process-page :deep(.el-date-editor .el-input__wrapper) {
  border-radius: 6px;
}



/* 滚动条样式 */
.order-process-page .overflow-y-auto::-webkit-scrollbar {
  width: 4px;
}

.order-process-page .overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.order-process-page .overflow-y-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.order-process-page .overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}



.chart-container {
  display: flex;
  gap: 1rem;
}

.trend-chart {
  flex: 7;
}

.pie-chart {
  flex: 3;
}

</style>
